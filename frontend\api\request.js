// HTTP请求封装
import axios from 'axios'
import { ElMessage, ElLoading } from 'element-plus'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId()

    // 显示加载状态（可选）
    if (config.showLoading !== false) {
      config.loadingInstance = ElLoading.service({
        text: '请求处理中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 关闭加载状态
    if (response.config.loadingInstance) {
      response.config.loadingInstance.close()
    }

    // 统一处理响应数据
    const { code, message, data } = response.data

    if (code === 200 || code === 0) {
      return { data, message }
    } else {
      // 业务错误处理
      ElMessage.error(message || '请求失败')
      return Promise.reject(new Error(message || '请求失败'))
    }
  },
  (error) => {
    // 关闭加载状态
    if (error.config?.loadingInstance) {
      error.config.loadingInstance.close()
    }

    // 网络错误处理
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('登录已过期，请重新登录')
          // 清除token并跳转到登录页
          localStorage.removeItem('auth_token')
          window.location.href = '/login'
          break
        case 403:
          ElMessage.error('没有权限访问该资源')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 429:
          ElMessage.error('请求过于频繁，请稍后再试')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络设置')
    } else {
      ElMessage.error('请求配置错误')
    }

    return Promise.reject(error)
  }
)

// 生成请求ID
function generateRequestId() {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 请求重试机制
const retryRequest = (config, retryCount = 3) => {
  return new Promise((resolve, reject) => {
    const attempt = (count) => {
      request(config)
        .then(resolve)
        .catch((error) => {
          if (count > 0 && isRetryableError(error)) {
            console.log(`请求失败，正在重试... 剩余次数: ${count}`)
            setTimeout(() => attempt(count - 1), 1000 * (4 - count))
          } else {
            reject(error)
          }
        })
    }
    attempt(retryCount)
  })
}

// 判断是否为可重试的错误
function isRetryableError(error) {
  if (!error.response) return true // 网络错误
  const status = error.response.status
  return status >= 500 || status === 429 // 服务器错误或限流
}

// 请求缓存机制
const requestCache = new Map()

const cachedRequest = (config, cacheTime = 5 * 60 * 1000) => {
  const cacheKey = `${config.method}_${config.url}_${JSON.stringify(config.params)}`
  const cached = requestCache.get(cacheKey)
  
  if (cached && Date.now() - cached.timestamp < cacheTime) {
    return Promise.resolve(cached.data)
  }
  
  return request(config).then(response => {
    requestCache.set(cacheKey, {
      data: response,
      timestamp: Date.now()
    })
    return response
  })
}

// 并发请求控制
class RequestQueue {
  constructor(maxConcurrent = 6) {
    this.maxConcurrent = maxConcurrent
    this.running = 0
    this.queue = []
  }

  add(requestFn) {
    return new Promise((resolve, reject) => {
      this.queue.push({
        requestFn,
        resolve,
        reject
      })
      this.process()
    })
  }

  async process() {
    if (this.running >= this.maxConcurrent || this.queue.length === 0) {
      return
    }

    this.running++
    const { requestFn, resolve, reject } = this.queue.shift()

    try {
      const result = await requestFn()
      resolve(result)
    } catch (error) {
      reject(error)
    } finally {
      this.running--
      this.process()
    }
  }
}

const requestQueue = new RequestQueue()

// 导出封装的请求方法
export default {
  // 基础请求方法
  request,
  
  // GET请求
  get: (url, config = {}) => request.get(url, config),
  
  // POST请求
  post: (url, data, config = {}) => request.post(url, data, config),
  
  // PUT请求
  put: (url, data, config = {}) => request.put(url, data, config),
  
  // DELETE请求
  delete: (url, config = {}) => request.delete(url, config),
  
  // 带重试的请求
  retryRequest,
  
  // 带缓存的请求
  cachedRequest,
  
  // 队列请求
  queueRequest: (requestFn) => requestQueue.add(requestFn),
  
  // 批量请求
  all: (requests) => Promise.all(requests),
  
  // 上传文件
  upload: (url, formData, config = {}) => {
    return request.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config.headers
      },
      onUploadProgress: config.onProgress
    })
  },
  
  // 下载文件
  download: (url, filename, config = {}) => {
    return request.get(url, {
      ...config,
      responseType: 'blob'
    }).then(response => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename
      link.click()
      window.URL.revokeObjectURL(downloadUrl)
      return response
    })
  }
}

// 请求统计和监控
export const requestStats = {
  total: 0,
  success: 0,
  error: 0,
  
  reset() {
    this.total = 0
    this.success = 0
    this.error = 0
  },
  
  getStats() {
    return {
      total: this.total,
      success: this.success,
      error: this.error,
      successRate: this.total > 0 ? (this.success / this.total * 100).toFixed(2) : 0
    }
  }
}

// 监听请求统计
request.interceptors.request.use(config => {
  requestStats.total++
  return config
})

request.interceptors.response.use(
  response => {
    requestStats.success++
    return response
  },
  error => {
    requestStats.error++
    return Promise.reject(error)
  }
)
