// API统一入口
import * as aiApi from './aiApi'
import * as voteApi from './voteApi'
import http from './http'

// 导出所有API
export {
  aiApi,
  voteApi,
  http
}

// 也可以单独导出常用的API
export const {
  generateVoteSchema,
  optimizeVoteSchema,
  generateAnalysis,
  recommendOptions,
  validateSchema,
  generateInvitation,
  getModelStatus,
  testConnection
} = aiApi

export const {
  createVote,
  submitVote,
  getVoteDetail,
  getVoteResults,
  getRealTimeStats,
  getVoteList,
  getUserVotes,
  updateVote,
  deleteVote,
  exportVoteData,
  getVoteStatistics,
  copyVote,
  setVoteStatus
} = voteApi

// 通用API
export const testApi = async () => {
  return await http.get('/test/health')
}

export const getSystemInfo = async () => {
  return await http.get('/test/info')
}

export default {
  ai: aiApi,
  vote: voteApi,
  http,
  testApi,
  getSystemInfo
}
