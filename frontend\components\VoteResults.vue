<template>
  <div class="vote-results">
    <div class="results-header">
      <h3>投票统计结果</h3>
      <div class="stats-summary">
        <el-statistic title="总投票数" :value="totalVotes" />
        <el-statistic title="参与率" :value="participationRate" suffix="%" />
      </div>
    </div>

    <div class="results-content">
      <div
        v-for="field in schema.fields"
        :key="field.id"
        class="field-result"
      >
        <h4 class="field-title">{{ field.label }}</h4>
        
        <!-- 单选/多选结果 -->
        <div v-if="field.type === 'radio' || field.type === 'checkbox'" class="choice-results">
          <div
            v-for="option in field.options"
            :key="option.id"
            class="option-result"
          >
            <div class="option-header">
              <span class="option-label">{{ option.label }}</span>
              <span class="option-count">
                {{ getOptionCount(field.id, option.value) }} 票
                ({{ getOptionPercentage(field.id, option.value) }}%)
              </span>
            </div>
            <el-progress
              :percentage="getOptionPercentage(field.id, option.value)"
              :color="getProgressColor(getOptionPercentage(field.id, option.value))"
              :stroke-width="8"
            />
          </div>
        </div>

        <!-- 评分结果 -->
        <div v-else-if="field.type === 'rating'" class="rating-results">
          <div class="rating-summary">
            <el-statistic
              title="平均评分"
              :value="getAverageRating(field.id)"
              :precision="1"
              suffix="分"
            />
            <el-statistic
              title="评分人数"
              :value="getRatingCount(field.id)"
              suffix="人"
            />
          </div>
          
          <div class="rating-distribution">
            <div
              v-for="(count, rating) in getRatingDistribution(field.id)"
              :key="rating"
              class="rating-bar"
            >
              <span class="rating-label">{{ rating }}星</span>
              <el-progress
                :percentage="(count / getRatingCount(field.id)) * 100"
                :stroke-width="6"
                :show-text="false"
              />
              <span class="rating-count">{{ count }}</span>
            </div>
          </div>
        </div>

        <!-- 排序结果 -->
        <div v-else-if="field.type === 'ranking'" class="ranking-results">
          <div class="ranking-table">
            <el-table :data="getRankingResults(field.id)" style="width: 100%">
              <el-table-column prop="rank" label="排名" width="80" align="center">
                <template #default="{ row }">
                  <el-tag
                    :type="getRankTagType(row.rank)"
                    effect="dark"
                    round
                  >
                    {{ row.rank }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="option" label="选项" />
              <el-table-column prop="avgPosition" label="平均位置" width="100" align="center">
                <template #default="{ row }">
                  {{ row.avgPosition.toFixed(1) }}
                </template>
              </el-table-column>
              <el-table-column prop="score" label="得分" width="100" align="center">
                <template #default="{ row }">
                  {{ row.score.toFixed(1) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表展示 -->
    <div class="charts-section" v-if="showCharts">
      <h4>可视化图表</h4>
      <div class="charts-grid">
        <div
          v-for="field in schema.fields"
          :key="`chart-${field.id}`"
          class="chart-container"
        >
          <VoteChart
            :field="field"
            :data="getChartData(field)"
            :type="getChartType(field.type)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import VoteChart from './VoteChart.vue'

// Props
const props = defineProps({
  results: {
    type: Object,
    required: true
  },
  schema: {
    type: Object,
    required: true
  },
  showCharts: {
    type: Boolean,
    default: true
  }
})

// 计算总投票数
const totalVotes = computed(() => {
  return props.results.totalVotes || 0
})

// 计算参与率
const participationRate = computed(() => {
  const total = props.results.totalInvited || totalVotes.value
  return total > 0 ? ((totalVotes.value / total) * 100).toFixed(1) : 0
})

// 获取选项投票数
const getOptionCount = (fieldId, optionValue) => {
  const fieldResult = props.results.fields?.[fieldId]
  if (!fieldResult) return 0
  
  return fieldResult.options?.[optionValue] || 0
}

// 获取选项投票百分比
const getOptionPercentage = (fieldId, optionValue) => {
  const count = getOptionCount(fieldId, optionValue)
  const total = totalVotes.value
  
  return total > 0 ? ((count / total) * 100).toFixed(1) : 0
}

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage >= 60) return '#67c23a'
  if (percentage >= 30) return '#e6a23c'
  return '#f56c6c'
}

// 获取平均评分
const getAverageRating = (fieldId) => {
  const fieldResult = props.results.fields?.[fieldId]
  if (!fieldResult?.ratings) return 0
  
  const ratings = fieldResult.ratings
  const total = ratings.reduce((sum, rating, index) => sum + rating * (index + 1), 0)
  const count = ratings.reduce((sum, rating) => sum + rating, 0)
  
  return count > 0 ? total / count : 0
}

// 获取评分人数
const getRatingCount = (fieldId) => {
  const fieldResult = props.results.fields?.[fieldId]
  if (!fieldResult?.ratings) return 0
  
  return fieldResult.ratings.reduce((sum, rating) => sum + rating, 0)
}

// 获取评分分布
const getRatingDistribution = (fieldId) => {
  const fieldResult = props.results.fields?.[fieldId]
  if (!fieldResult?.ratings) return {}
  
  const distribution = {}
  fieldResult.ratings.forEach((count, index) => {
    distribution[index + 1] = count
  })
  
  return distribution
}

// 获取排序结果
const getRankingResults = (fieldId) => {
  const fieldResult = props.results.fields?.[fieldId]
  if (!fieldResult?.rankings) return []
  
  const rankings = fieldResult.rankings
  const results = []
  
  // 计算每个选项的平均位置和得分
  Object.keys(rankings).forEach(optionValue => {
    const positions = rankings[optionValue]
    const avgPosition = positions.reduce((sum, pos) => sum + pos, 0) / positions.length
    const score = positions.reduce((sum, pos) => sum + (positions.length - pos + 1), 0) / positions.length
    
    results.push({
      option: optionValue,
      avgPosition,
      score,
      count: positions.length
    })
  })
  
  // 按得分排序
  results.sort((a, b) => b.score - a.score)
  
  // 添加排名
  results.forEach((item, index) => {
    item.rank = index + 1
  })
  
  return results
}

// 获取排名标签类型
const getRankTagType = (rank) => {
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

// 获取图表数据
const getChartData = (field) => {
  const fieldResult = props.results.fields?.[field.id]
  if (!fieldResult) return []
  
  switch (field.type) {
    case 'radio':
    case 'checkbox':
      return field.options.map(option => ({
        name: option.label,
        value: getOptionCount(field.id, option.value)
      }))
    
    case 'rating':
      return Object.entries(getRatingDistribution(field.id)).map(([rating, count]) => ({
        name: `${rating}星`,
        value: count
      }))
    
    case 'ranking':
      return getRankingResults(field.id).map(item => ({
        name: item.option,
        value: item.score
      }))
    
    default:
      return []
  }
}

// 获取图表类型
const getChartType = (fieldType) => {
  switch (fieldType) {
    case 'radio':
    case 'checkbox':
      return 'pie'
    case 'rating':
      return 'bar'
    case 'ranking':
      return 'bar'
    default:
      return 'bar'
  }
}
</script>

<style scoped>
.vote-results {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.results-header h3 {
  margin: 0;
  color: #303133;
}

.stats-summary {
  display: flex;
  gap: 32px;
}

.field-result {
  margin-bottom: 32px;
}

.field-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

.choice-results {
  space-y: 12px;
}

.option-result {
  margin-bottom: 16px;
}

.option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.option-label {
  font-weight: 500;
  color: #606266;
}

.option-count {
  font-size: 14px;
  color: #909399;
}

.rating-results {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
}

.rating-summary {
  display: flex;
  gap: 32px;
  margin-bottom: 24px;
  justify-content: center;
}

.rating-distribution {
  space-y: 8px;
}

.rating-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.rating-label {
  width: 60px;
  font-size: 14px;
  color: #606266;
}

.rating-count {
  width: 40px;
  text-align: right;
  font-size: 14px;
  color: #909399;
}

.ranking-results {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
}

.charts-section {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

.charts-section h4 {
  margin-bottom: 16px;
  color: #303133;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.chart-container {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
  min-height: 300px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .results-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .stats-summary {
    gap: 16px;
  }
  
  .rating-summary {
    flex-direction: column;
    gap: 16px;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .option-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
