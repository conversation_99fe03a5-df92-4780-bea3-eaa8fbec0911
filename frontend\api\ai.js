// AI相关API接口
import request from './request'

/**
 * 生成投票表单Schema
 * @param {string} prompt - AI提示词
 * @returns {Promise} 返回生成的表单Schema和Vue组件代码
 */
export const generateVoteSchemaAPI = async (prompt) => {
  try {
    const response = await request.post('/ai/generate-vote-form', {
      prompt,
      model: 'gpt-4', // 或者使用其他模型
      temperature: 0.7,
      max_tokens: 4000
    })
    
    return response.data
  } catch (error) {
    console.error('AI生成失败:', error)
    throw error
  }
}

/**
 * 优化投票表单
 * @param {Object} schema - 当前表单Schema
 * @param {string} feedback - 用户反馈
 * @returns {Promise} 返回优化后的Schema
 */
export const optimizeVoteSchemaAPI = async (schema, feedback) => {
  try {
    const response = await request.post('/ai/optimize-vote-form', {
      schema,
      feedback,
      model: 'gpt-4',
      temperature: 0.5
    })
    
    return response.data
  } catch (error) {
    console.error('表单优化失败:', error)
    throw error
  }
}

/**
 * 生成投票分析报告
 * @param {Object} voteResults - 投票结果数据
 * @param {Object} schema - 表单Schema
 * @returns {Promise} 返回分析报告
 */
export const generateAnalysisReportAPI = async (voteResults, schema) => {
  try {
    const response = await request.post('/ai/generate-analysis', {
      voteResults,
      schema,
      model: 'gpt-4',
      temperature: 0.3
    })
    
    return response.data
  } catch (error) {
    console.error('分析报告生成失败:', error)
    throw error
  }
}

/**
 * 智能推荐投票选项
 * @param {string} topic - 投票主题
 * @param {string} type - 投票类型
 * @param {number} count - 需要的选项数量
 * @returns {Promise} 返回推荐的选项列表
 */
export const recommendOptionsAPI = async (topic, type, count = 5) => {
  try {
    const response = await request.post('/ai/recommend-options', {
      topic,
      type,
      count,
      model: 'gpt-3.5-turbo',
      temperature: 0.8
    })
    
    return response.data
  } catch (error) {
    console.error('选项推荐失败:', error)
    throw error
  }
}

/**
 * 验证表单Schema的合理性
 * @param {Object} schema - 表单Schema
 * @returns {Promise} 返回验证结果和建议
 */
export const validateSchemaAPI = async (schema) => {
  try {
    const response = await request.post('/ai/validate-schema', {
      schema,
      model: 'gpt-3.5-turbo',
      temperature: 0.2
    })
    
    return response.data
  } catch (error) {
    console.error('Schema验证失败:', error)
    throw error
  }
}

/**
 * 生成投票邀请文案
 * @param {Object} schema - 表单Schema
 * @param {string} target - 目标受众
 * @returns {Promise} 返回邀请文案
 */
export const generateInvitationAPI = async (schema, target) => {
  try {
    const response = await request.post('/ai/generate-invitation', {
      schema,
      target,
      model: 'gpt-3.5-turbo',
      temperature: 0.7
    })
    
    return response.data
  } catch (error) {
    console.error('邀请文案生成失败:', error)
    throw error
  }
}

// 模拟AI响应（用于开发测试）
export const mockAIResponse = {
  generateVoteSchema: (prompt) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            formSchema: {
              id: `vote_${Date.now()}`,
              title: "公司午餐选择投票",
              description: "请选择您希望公司提供的午餐类型",
              type: "single",
              settings: {
                anonymous: true,
                allowMultiple: false,
                timeLimit: null,
                required: true
              },
              fields: [
                {
                  id: "lunch_choice",
                  type: "radio",
                  label: "您希望公司提供哪种类型的午餐？",
                  description: "请根据您的喜好选择一种午餐类型",
                  required: true,
                  options: [
                    {
                      id: "option_1",
                      label: "中式快餐",
                      value: "chinese",
                      description: "包括盖浇饭、炒菜等"
                    },
                    {
                      id: "option_2",
                      label: "西式简餐",
                      value: "western",
                      description: "包括汉堡、三明治、沙拉等"
                    },
                    {
                      id: "option_3",
                      label: "日韩料理",
                      value: "asian",
                      description: "包括寿司、拉面、石锅拌饭等"
                    },
                    {
                      id: "option_4",
                      label: "健康轻食",
                      value: "healthy",
                      description: "低卡路里、高营养的轻食选择"
                    }
                  ],
                  validation: {
                    required: true
                  }
                }
              ]
            },
            vueComponent: `<template>
  <div class="vote-form">
    <h2>{{ schema.title }}</h2>
    <p>{{ schema.description }}</p>
    <!-- 表单内容 -->
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const props = defineProps(['schema'])
const formData = reactive({})

// 表单逻辑
</script>`,
            styling: {
              theme: "modern",
              primaryColor: "#409EFF",
              layout: "vertical"
            }
          }
        })
      }, 1500)
    })
  }
}

// 开发环境使用模拟数据
if (process.env.NODE_ENV === 'development') {
  // 可以在这里覆盖API函数使用模拟数据
  // generateVoteSchemaAPI = mockAIResponse.generateVoteSchema
}
