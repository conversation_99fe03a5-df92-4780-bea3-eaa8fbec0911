<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI投票系统 - 调试模式</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
    }
    
    #loading {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
    }
    
    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #409eff;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div id="app">
    <div id="loading">
      <div class="spinner"></div>
      <p>正在加载应用...</p>
      <p style="font-size: 12px; color: #666;">如果长时间显示此页面，请检查浏览器控制台</p>
    </div>
  </div>
  
  <script>
    // 基础错误捕获
    window.addEventListener('error', function(e) {
      console.error('全局错误:', e.error)
      document.getElementById('loading').innerHTML = `
        <h2 style="color: red;">应用加载失败</h2>
        <p>错误: ${e.error?.message || e.message}</p>
        <p>文件: ${e.filename}:${e.lineno}</p>
        <p>请检查浏览器控制台获取详细信息</p>
      `
    })
    
    // 未处理的Promise错误
    window.addEventListener('unhandledrejection', function(e) {
      console.error('未处理的Promise错误:', e.reason)
    })
    
    console.log('HTML页面加载完成')
  </script>
  
  <script type="module" src="/main-simple.js"></script>
</body>
</html>
