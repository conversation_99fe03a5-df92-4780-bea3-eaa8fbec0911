<template>
  <div id="app">
    <el-container class="app-container">
      <!-- 头部 -->
      <el-header class="app-header">
        <div class="header-content">
          <h1 class="app-title">
            <el-icon><DataAnalysis /></el-icon>
            AI辅助投票生成系统
          </h1>
          <div class="header-actions">
            <el-button type="primary" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              创建投票
            </el-button>
          </div>
        </div>
      </el-header>

      <!-- 主内容 -->
      <el-main class="app-main">
        <!-- 创建投票表单 -->
        <el-card v-if="!currentVoteSchema" class="create-card">
          <template #header>
            <div class="card-header">
              <span>快速创建投票</span>
            </div>
          </template>
          
          <el-form
            ref="createFormRef"
            :model="createForm"
            :rules="createRules"
            label-width="120px"
            class="create-form"
          >
            <el-form-item label="投票主题" prop="topic">
              <el-input
                v-model="createForm.topic"
                placeholder="请输入投票主题，如：公司午餐选择"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="投票类型" prop="type">
              <el-radio-group v-model="createForm.type">
                <el-radio label="single">单选投票</el-radio>
                <el-radio label="multiple">多选投票</el-radio>
                <el-radio label="rating">评分投票</el-radio>
                <el-radio label="ranking">排序投票</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="选项设置" prop="optionMode">
              <el-radio-group v-model="createForm.optionMode">
                <el-radio label="auto">AI自动生成</el-radio>
                <el-radio label="manual">手动输入</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item
              v-if="createForm.optionMode === 'manual'"
              label="投票选项"
              prop="options"
            >
              <div class="options-input">
                <el-input
                  v-for="(option, index) in createForm.options"
                  :key="index"
                  v-model="createForm.options[index]"
                  :placeholder="`选项 ${index + 1}`"
                  class="option-input"
                >
                  <template #append>
                    <el-button
                      @click="removeOption(index)"
                      :disabled="createForm.options.length <= 2"
                      type="danger"
                      text
                    >
                      删除
                    </el-button>
                  </template>
                </el-input>
                <el-button
                  @click="addOption"
                  type="primary"
                  text
                  :disabled="createForm.options.length >= 10"
                >
                  <el-icon><Plus /></el-icon>
                  添加选项
                </el-button>
              </div>
            </el-form-item>

            <el-form-item label="高级设置">
              <el-checkbox v-model="createForm.anonymous">匿名投票</el-checkbox>
              <el-checkbox v-model="createForm.hasTimeLimit">设置截止时间</el-checkbox>
            </el-form-item>

            <el-form-item
              v-if="createForm.hasTimeLimit"
              label="截止时间"
              prop="timeLimit"
            >
              <el-date-picker
                v-model="createForm.timeLimit"
                type="datetime"
                placeholder="选择截止时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DDTHH:mm:ss.sssZ"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                @click="generateVoteForm"
                :loading="generating"
                size="large"
              >
                <el-icon><Magic /></el-icon>
                AI生成投票表单
              </el-button>
              <el-button @click="resetCreateForm" size="large">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 生成的投票表单 -->
        <div v-if="currentVoteSchema" class="vote-container">
          <div class="vote-actions">
            <el-button @click="backToCreate">
              <el-icon><ArrowLeft /></el-icon>
              返回创建
            </el-button>
            <el-button type="success" @click="exportSchema">
              <el-icon><Download /></el-icon>
              导出Schema
            </el-button>
            <el-button type="warning" @click="previewCode">
              <el-icon><View /></el-icon>
              预览代码
            </el-button>
          </div>

          <DynamicVoteForm
            :form-schema="currentVoteSchema"
            @submit="handleVoteSubmit"
          />
        </div>
      </el-main>
    </el-container>

    <!-- 代码预览对话框 -->
    <el-dialog
      v-model="showCodeDialog"
      title="Vue组件代码预览"
      width="80%"
      :before-close="handleCodeDialogClose"
    >
      <el-tabs v-model="activeCodeTab">
        <el-tab-pane label="Schema JSON" name="schema">
          <el-input
            v-model="schemaCode"
            type="textarea"
            :rows="20"
            readonly
            class="code-textarea"
          />
        </el-tab-pane>
        <el-tab-pane label="Vue组件" name="component">
          <el-input
            v-model="componentCode"
            type="textarea"
            :rows="20"
            readonly
            class="code-textarea"
          />
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <el-button @click="copyCode">
          <el-icon><CopyDocument /></el-icon>
          复制代码
        </el-button>
        <el-button type="primary" @click="showCodeDialog = false">
          关闭
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DataAnalysis,
  Plus,
  Magic,
  ArrowLeft,
  Download,
  View,
  CopyDocument
} from '@element-plus/icons-vue'
import DynamicVoteForm from './components/DynamicVoteForm.vue'
import { generateVoteSchema } from './api/index.js'

// 响应式数据
const createFormRef = ref()
const generating = ref(false)
const showCodeDialog = ref(false)
const activeCodeTab = ref('schema')
const currentVoteSchema = ref(null)
const schemaCode = ref('')
const componentCode = ref('')

// 创建表单数据
const createForm = reactive({
  topic: '',
  type: 'single',
  optionMode: 'auto',
  options: ['', ''],
  anonymous: true,
  hasTimeLimit: false,
  timeLimit: null
})

// 表单验证规则
const createRules = reactive({
  topic: [
    { required: true, message: '请输入投票主题', trigger: 'blur' },
    { min: 2, max: 100, message: '主题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择投票类型', trigger: 'change' }
  ],
  optionMode: [
    { required: true, message: '请选择选项设置方式', trigger: 'change' }
  ],
  options: [
    {
      validator: (rule, value, callback) => {
        if (createForm.optionMode === 'manual') {
          const validOptions = value.filter(opt => opt.trim())
          if (validOptions.length < 2) {
            callback(new Error('至少需要2个有效选项'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  timeLimit: [
    {
      validator: (rule, value, callback) => {
        if (createForm.hasTimeLimit && !value) {
          callback(new Error('请选择截止时间'))
        } else if (createForm.hasTimeLimit && new Date(value) <= new Date()) {
          callback(new Error('截止时间必须晚于当前时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
})

// 添加选项
const addOption = () => {
  if (createForm.options.length < 10) {
    createForm.options.push('')
  }
}

// 删除选项
const removeOption = (index) => {
  if (createForm.options.length > 2) {
    createForm.options.splice(index, 1)
  }
}

// 生成投票表单
const generateVoteForm = async () => {
  try {
    await createFormRef.value.validate()
    
    generating.value = true
    
    // 构建AI提示词
    const prompt = buildAIPrompt()
    
    // 调用AI API生成表单Schema
    const response = await generateVoteSchema(prompt)
    
    currentVoteSchema.value = response.formSchema
    schemaCode.value = JSON.stringify(response.formSchema, null, 2)
    componentCode.value = response.vueComponent || '// Vue组件代码将在这里显示'
    
    ElMessage.success('投票表单生成成功！')
    
  } catch (error) {
    console.error('生成失败:', error)
    ElMessage.error('生成失败，请重试')
  } finally {
    generating.value = false
  }
}

// 构建AI提示词
const buildAIPrompt = () => {
  let prompt = `请生成一个投票表单，要求如下：
- 投票主题：${createForm.topic}
- 投票类型：${getTypeDescription(createForm.type)}
- 匿名投票：${createForm.anonymous ? '是' : '否'}`

  if (createForm.hasTimeLimit && createForm.timeLimit) {
    prompt += `\n- 截止时间：${createForm.timeLimit}`
  }

  if (createForm.optionMode === 'manual') {
    const validOptions = createForm.options.filter(opt => opt.trim())
    prompt += `\n- 投票选项：${validOptions.join('、')}`
  } else {
    prompt += `\n- 选项生成：请根据主题自动生成3-6个相关选项`
  }

  return prompt
}

// 获取类型描述
const getTypeDescription = (type) => {
  const descriptions = {
    single: '单选投票（只能选择一个选项）',
    multiple: '多选投票（可以选择多个选项）',
    rating: '评分投票（对选项进行评分）',
    ranking: '排序投票（对选项进行排序）'
  }
  return descriptions[type] || type
}

// 重置创建表单
const resetCreateForm = () => {
  createFormRef.value.resetFields()
  createForm.options = ['', '']
}

// 返回创建页面
const backToCreate = () => {
  currentVoteSchema.value = null
}

// 导出Schema
const exportSchema = () => {
  const dataStr = JSON.stringify(currentVoteSchema.value, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `vote-schema-${Date.now()}.json`
  link.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('Schema导出成功！')
}

// 预览代码
const previewCode = () => {
  showCodeDialog.value = true
}

// 关闭代码对话框
const handleCodeDialogClose = () => {
  showCodeDialog.value = false
}

// 复制代码
const copyCode = async () => {
  try {
    const code = activeCodeTab.value === 'schema' ? schemaCode.value : componentCode.value
    await navigator.clipboard.writeText(code)
    ElMessage.success('代码已复制到剪贴板！')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 处理投票提交
const handleVoteSubmit = (voteData) => {
  console.log('投票数据:', voteData)
  ElMessage.success('投票提交成功！')
}

// 组件挂载时的初始化
onMounted(() => {
  // 可以在这里加载示例数据或用户历史记录
})
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  height: 100%;
}

.app-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.app-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.create-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card-header {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.create-form {
  max-width: 600px;
  margin: 0 auto;
}

.options-input {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-input {
  margin-bottom: 8px;
}

.vote-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.vote-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.code-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-main {
    padding: 16px;
  }
  
  .header-content {
    padding: 0 16px;
  }
  
  .app-title {
    font-size: 16px;
  }
  
  .vote-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .create-form {
    max-width: 100%;
  }
}
</style>
