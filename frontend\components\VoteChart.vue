<template>
  <div class="vote-chart">
    <h5 class="chart-title">{{ field.label }}</h5>
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'

// Props
const props = defineProps({
  field: {
    type: Object,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
  type: {
    type: String,
    default: 'bar'
  }
})

// Refs
const chartContainer = ref()

// 简单的图表渲染（使用Canvas）
const renderChart = () => {
  if (!chartContainer.value || !props.data.length) return

  const container = chartContainer.value
  container.innerHTML = ''

  if (props.type === 'pie') {
    renderPieChart(container)
  } else {
    renderBarChart(container)
  }
}

// 渲染饼图
const renderPieChart = (container) => {
  const canvas = document.createElement('canvas')
  canvas.width = 300
  canvas.height = 300
  container.appendChild(canvas)

  const ctx = canvas.getContext('2d')
  const centerX = canvas.width / 2
  const centerY = canvas.height / 2
  const radius = 100

  const total = props.data.reduce((sum, item) => sum + item.value, 0)
  let currentAngle = -Math.PI / 2

  const colors = [
    '#409EFF', '#67C23A', '#E6A23C', '#F56C6C',
    '#909399', '#C0C4CC', '#606266', '#303133'
  ]

  props.data.forEach((item, index) => {
    const sliceAngle = (item.value / total) * 2 * Math.PI
    
    // 绘制扇形
    ctx.beginPath()
    ctx.moveTo(centerX, centerY)
    ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle)
    ctx.closePath()
    ctx.fillStyle = colors[index % colors.length]
    ctx.fill()
    ctx.strokeStyle = '#fff'
    ctx.lineWidth = 2
    ctx.stroke()

    // 绘制标签
    const labelAngle = currentAngle + sliceAngle / 2
    const labelX = centerX + Math.cos(labelAngle) * (radius + 20)
    const labelY = centerY + Math.sin(labelAngle) * (radius + 20)
    
    ctx.fillStyle = '#303133'
    ctx.font = '12px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(item.name, labelX, labelY)
    
    // 绘制百分比
    const percentage = ((item.value / total) * 100).toFixed(1) + '%'
    ctx.fillText(percentage, labelX, labelY + 15)

    currentAngle += sliceAngle
  })
}

// 渲染柱状图
const renderBarChart = (container) => {
  const canvas = document.createElement('canvas')
  canvas.width = 400
  canvas.height = 300
  container.appendChild(canvas)

  const ctx = canvas.getContext('2d')
  const padding = 40
  const chartWidth = canvas.width - padding * 2
  const chartHeight = canvas.height - padding * 2

  const maxValue = Math.max(...props.data.map(item => item.value))
  const barWidth = chartWidth / props.data.length * 0.8
  const barSpacing = chartWidth / props.data.length * 0.2

  // 绘制坐标轴
  ctx.strokeStyle = '#E4E7ED'
  ctx.lineWidth = 1
  
  // Y轴
  ctx.beginPath()
  ctx.moveTo(padding, padding)
  ctx.lineTo(padding, padding + chartHeight)
  ctx.stroke()
  
  // X轴
  ctx.beginPath()
  ctx.moveTo(padding, padding + chartHeight)
  ctx.lineTo(padding + chartWidth, padding + chartHeight)
  ctx.stroke()

  // 绘制柱子
  props.data.forEach((item, index) => {
    const barHeight = (item.value / maxValue) * chartHeight
    const x = padding + index * (barWidth + barSpacing) + barSpacing / 2
    const y = padding + chartHeight - barHeight

    // 柱子
    ctx.fillStyle = '#409EFF'
    ctx.fillRect(x, y, barWidth, barHeight)

    // 数值标签
    ctx.fillStyle = '#303133'
    ctx.font = '12px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(item.value.toString(), x + barWidth / 2, y - 5)

    // 名称标签
    ctx.save()
    ctx.translate(x + barWidth / 2, padding + chartHeight + 15)
    ctx.rotate(-Math.PI / 4)
    ctx.textAlign = 'right'
    ctx.fillText(item.name, 0, 0)
    ctx.restore()
  })

  // 绘制Y轴刻度
  const steps = 5
  for (let i = 0; i <= steps; i++) {
    const value = (maxValue / steps) * i
    const y = padding + chartHeight - (value / maxValue) * chartHeight
    
    ctx.fillStyle = '#909399'
    ctx.font = '10px Arial'
    ctx.textAlign = 'right'
    ctx.fillText(Math.round(value).toString(), padding - 5, y + 3)
    
    // 网格线
    if (i > 0) {
      ctx.strokeStyle = '#F5F7FA'
      ctx.lineWidth = 1
      ctx.beginPath()
      ctx.moveTo(padding, y)
      ctx.lineTo(padding + chartWidth, y)
      ctx.stroke()
    }
  }
}

// 监听数据变化
watch(() => props.data, () => {
  nextTick(() => {
    renderChart()
  })
}, { deep: true })

// 组件挂载时渲染图表
onMounted(() => {
  nextTick(() => {
    renderChart()
  })
})
</script>

<style scoped>
.vote-chart {
  background: #fff;
  border-radius: 6px;
  padding: 16px;
  text-align: center;
}

.chart-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

.chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.chart-container canvas {
  max-width: 100%;
  height: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-container canvas {
    width: 100% !important;
    height: auto !important;
  }
}
</style>
