// 投票相关API接口
import request from './request'

/**
 * 提交投票
 * @param {Object} voteData - 投票数据
 * @returns {Promise} 返回提交结果
 */
export const submitVoteAPI = async (voteData) => {
  try {
    const response = await request.post('/api/votes/submit', voteData)
    return response.data
  } catch (error) {
    console.error('投票提交失败:', error)
    throw error
  }
}

/**
 * 获取投票结果
 * @param {string} voteId - 投票ID
 * @returns {Promise} 返回投票结果
 */
export const getVoteResultsAPI = async (voteId) => {
  try {
    const response = await request.get(`/api/votes/${voteId}/results`)
    return response.data
  } catch (error) {
    console.error('获取投票结果失败:', error)
    throw error
  }
}

/**
 * 创建投票
 * @param {Object} voteSchema - 投票表单Schema
 * @returns {Promise} 返回创建结果
 */
export const createVoteAPI = async (voteSchema) => {
  try {
    const response = await request.post('/api/votes/create', voteSchema)
    return response.data
  } catch (error) {
    console.error('创建投票失败:', error)
    throw error
  }
}

/**
 * 获取投票详情
 * @param {string} voteId - 投票ID
 * @returns {Promise} 返回投票详情
 */
export const getVoteDetailAPI = async (voteId) => {
  try {
    const response = await request.get(`/api/votes/${voteId}`)
    return response.data
  } catch (error) {
    console.error('获取投票详情失败:', error)
    throw error
  }
}

/**
 * 获取用户投票列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回投票列表
 */
export const getUserVotesAPI = async (params = {}) => {
  try {
    const response = await request.get('/api/votes/user', { params })
    return response.data
  } catch (error) {
    console.error('获取投票列表失败:', error)
    throw error
  }
}

/**
 * 删除投票
 * @param {string} voteId - 投票ID
 * @returns {Promise} 返回删除结果
 */
export const deleteVoteAPI = async (voteId) => {
  try {
    const response = await request.delete(`/api/votes/${voteId}`)
    return response.data
  } catch (error) {
    console.error('删除投票失败:', error)
    throw error
  }
}

/**
 * 更新投票设置
 * @param {string} voteId - 投票ID
 * @param {Object} settings - 新的设置
 * @returns {Promise} 返回更新结果
 */
export const updateVoteSettingsAPI = async (voteId, settings) => {
  try {
    const response = await request.put(`/api/votes/${voteId}/settings`, settings)
    return response.data
  } catch (error) {
    console.error('更新投票设置失败:', error)
    throw error
  }
}

/**
 * 获取实时投票统计
 * @param {string} voteId - 投票ID
 * @returns {Promise} 返回实时统计数据
 */
export const getRealTimeStatsAPI = async (voteId) => {
  try {
    const response = await request.get(`/api/votes/${voteId}/stats`)
    return response.data
  } catch (error) {
    console.error('获取实时统计失败:', error)
    throw error
  }
}

/**
 * 导出投票数据
 * @param {string} voteId - 投票ID
 * @param {string} format - 导出格式 (excel, csv, json)
 * @returns {Promise} 返回导出文件
 */
export const exportVoteDataAPI = async (voteId, format = 'excel') => {
  try {
    const response = await request.get(`/api/votes/${voteId}/export`, {
      params: { format },
      responseType: 'blob'
    })
    return response.data
  } catch (error) {
    console.error('导出投票数据失败:', error)
    throw error
  }
}

// WebSocket连接管理
export class VoteWebSocket {
  constructor(voteId) {
    this.voteId = voteId
    this.ws = null
    this.listeners = new Map()
  }

  connect() {
    const wsUrl = `${process.env.VUE_APP_WS_URL}/votes/${this.voteId}`
    this.ws = new WebSocket(wsUrl)

    this.ws.onopen = () => {
      console.log('WebSocket连接已建立')
      this.emit('connected')
    }

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.emit(data.type, data.payload)
      } catch (error) {
        console.error('WebSocket消息解析失败:', error)
      }
    }

    this.ws.onclose = () => {
      console.log('WebSocket连接已关闭')
      this.emit('disconnected')
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error)
      this.emit('error', error)
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('WebSocket事件处理错误:', error)
        }
      })
    }
  }

  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data))
    }
  }
}
