// AI相关API接口 - 重构版本
import http from './http'

/**
 * 生成投票表单Schema
 */
export const generateVoteSchema = async (prompt) => {
  return await http.post('/ai/generate-vote-form', {
    prompt,
    model: 'gpt-4',
    temperature: 0.7,
    max_tokens: 4000
  })
}

/**
 * 优化投票表单
 */
export const optimizeVoteSchema = async (schema, feedback) => {
  return await http.post('/ai/optimize-vote-form', {
    schema,
    feedback
  })
}

/**
 * 生成投票分析报告
 */
export const generateAnalysis = async (voteId, schema) => {
  return await http.post('/ai/generate-analysis', {
    voteId,
    schema
  })
}

/**
 * 推荐投票选项
 */
export const recommendOptions = async (topic, type, count = 5) => {
  return await http.post('/ai/recommend-options', {
    topic,
    type,
    count
  })
}

/**
 * 验证表单Schema
 */
export const validateSchema = async (schema) => {
  return await http.post('/ai/validate-schema', {
    schema
  })
}

/**
 * 生成邀请文案
 */
export const generateInvitation = async (voteDetail, target) => {
  return await http.post('/ai/generate-invitation', {
    voteDetail,
    target
  })
}

/**
 * 获取AI模型状态
 */
export const getModelStatus = async () => {
  return await http.get('/ai/model-status')
}

/**
 * 测试AI连接
 */
export const testConnection = async () => {
  return await http.get('/ai/test-connection')
}
