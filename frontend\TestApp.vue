<template>
  <div id="app">
    <h1>API导入测试</h1>
    <el-button @click="testAiApi" type="primary">测试AI API</el-button>
    <el-button @click="testVoteApi" type="success">测试Vote API</el-button>
    <div v-if="result" :style="{ color: result.success ? 'green' : 'red' }">
      {{ result.message }}
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'TestApp',
  setup() {
    const result = ref(null)

    const testAiApi = async () => {
      try {
        // 动态导入AI API
        const { generateVoteSchemaAPI } = await import('./api/ai.js')
        console.log('AI API导入成功:', generateVoteSchemaAPI)
        result.value = { success: true, message: 'AI API导入成功！' }
      } catch (error) {
        console.error('AI API导入失败:', error)
        result.value = { success: false, message: `AI API导入失败: ${error.message}` }
      }
    }

    const testVoteApi = async () => {
      try {
        // 动态导入Vote API
        const { submitVoteAPI } = await import('./api/vote.js')
        console.log('Vote API导入成功:', submitVoteAPI)
        result.value = { success: true, message: 'Vote API导入成功！' }
      } catch (error) {
        console.error('Vote API导入失败:', error)
        result.value = { success: false, message: `Vote API导入失败: ${error.message}` }
      }
    }

    return {
      result,
      testAiApi,
      testVoteApi
    }
  }
}
</script>

<style>
#app {
  padding: 20px;
}
</style>
