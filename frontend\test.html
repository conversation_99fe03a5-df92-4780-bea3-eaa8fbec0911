<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
</head>
<body>
    <h1>API导入测试</h1>
    <div id="test-result"></div>
    
    <script type="module">
        try {
            // 测试导入AI API
            const aiModule = await import('./api/ai.js');
            console.log('AI模块导入成功:', aiModule);
            
            // 测试导入Vote API
            const voteModule = await import('./api/vote.js');
            console.log('Vote模块导入成功:', voteModule);
            
            document.getElementById('test-result').innerHTML = `
                <p style="color: green;">✅ API模块导入成功</p>
                <p>AI模块导出: ${Object.keys(aiModule).join(', ')}</p>
                <p>Vote模块导出: ${Object.keys(voteModule).join(', ')}</p>
            `;
        } catch (error) {
            console.error('模块导入失败:', error);
            document.getElementById('test-result').innerHTML = `
                <p style="color: red;">❌ API模块导入失败: ${error.message}</p>
            `;
        }
    </script>
</body>
</html>
