<template>
  <div id="simple-app">
    <h1>简单测试页面</h1>
    <p>如果你能看到这个页面，说明Vue基本功能正常</p>
    
    <div class="test-section">
      <h2>基础功能测试</h2>
      <p>当前时间: {{ currentTime }}</p>
      <button @click="updateTime">更新时间</button>
    </div>

    <div class="test-section">
      <h2>响应式测试</h2>
      <input v-model="message" placeholder="输入内容">
      <p>你输入的内容: {{ message }}</p>
    </div>

    <div class="test-section">
      <h2>API测试</h2>
      <button @click="testBasicAPI">测试基础API</button>
      <p v-if="apiResult">API结果: {{ apiResult }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const currentTime = ref('')
const message = ref('')
const apiResult = ref('')

// 更新时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

// 测试基础API
const testBasicAPI = async () => {
  try {
    // 简单的fetch测试
    const response = await fetch('/api/test/health')
    if (response.ok) {
      const data = await response.json()
      apiResult.value = '连接成功: ' + JSON.stringify(data)
    } else {
      apiResult.value = '连接失败: ' + response.status
    }
  } catch (error) {
    apiResult.value = '错误: ' + error.message
  }
}

// 组件挂载时初始化
onMounted(() => {
  updateTime()
  console.log('SimpleApp 组件已挂载')
})
</script>

<style scoped>
#simple-app {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.test-section {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

button {
  padding: 8px 16px;
  margin: 5px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #66b1ff;
}

input {
  padding: 8px;
  margin: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 200px;
}
</style>
