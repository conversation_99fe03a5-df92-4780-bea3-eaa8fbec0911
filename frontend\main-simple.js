// 简化的main.js - 用于调试白屏问题
import { createApp } from 'vue'
import SimpleApp from './SimpleApp.vue'

console.log('开始创建Vue应用...')

try {
  const app = createApp(SimpleApp)
  console.log('Vue应用创建成功')
  
  app.mount('#app')
  console.log('Vue应用挂载成功')
} catch (error) {
  console.error('Vue应用创建/挂载失败:', error)
  
  // 显示错误信息到页面
  document.body.innerHTML = `
    <div style="padding: 20px; color: red; font-family: Arial;">
      <h1>应用启动失败</h1>
      <p>错误信息: ${error.message}</p>
      <p>请检查浏览器控制台获取详细信息</p>
    </div>
  `
}
