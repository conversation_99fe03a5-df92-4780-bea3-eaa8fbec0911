<template>
  <div class="dynamic-vote-form">
    <!-- 表单头部 -->
    <div class="form-header">
      <h2 class="form-title">{{ formSchema.title }}</h2>
      <p class="form-description" v-if="formSchema.description">
        {{ formSchema.description }}
      </p>
      <div class="form-meta">
        <el-tag v-if="formSchema.settings.anonymous" type="info" size="small">
          匿名投票
        </el-tag>
        <el-tag v-if="formSchema.settings.timeLimit" type="warning" size="small">
          截止时间：{{ formatTime(formSchema.settings.timeLimit) }}
        </el-tag>
      </div>
    </div>

    <!-- 动态表单 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-position="top"
      class="vote-form"
    >
      <div
        v-for="field in formSchema.fields"
        :key="field.id"
        class="form-field"
      >
        <el-form-item
          :label="field.label"
          :prop="field.id"
          :required="field.required"
        >
          <p v-if="field.description" class="field-description">
            {{ field.description }}
          </p>

          <!-- 单选题 -->
          <el-radio-group
            v-if="field.type === 'radio'"
            v-model="formData[field.id]"
            class="radio-group"
          >
            <el-radio
              v-for="option in field.options"
              :key="option.id"
              :label="option.value"
              class="radio-option"
            >
              <div class="option-content">
                <span class="option-label">{{ option.label }}</span>
                <span v-if="option.description" class="option-description">
                  {{ option.description }}
                </span>
              </div>
            </el-radio>
          </el-radio-group>

          <!-- 多选题 -->
          <el-checkbox-group
            v-else-if="field.type === 'checkbox'"
            v-model="formData[field.id]"
            class="checkbox-group"
            :max="field.validation?.max"
          >
            <el-checkbox
              v-for="option in field.options"
              :key="option.id"
              :label="option.value"
              class="checkbox-option"
            >
              <div class="option-content">
                <span class="option-label">{{ option.label }}</span>
                <span v-if="option.description" class="option-description">
                  {{ option.description }}
                </span>
              </div>
            </el-checkbox>
          </el-checkbox-group>

          <!-- 评分题 -->
          <div v-else-if="field.type === 'rating'" class="rating-field">
            <el-rate
              v-model="formData[field.id]"
              :max="field.validation?.max || 5"
              :allow-half="field.validation?.allowHalf || false"
              show-text
              :texts="field.options?.map(opt => opt.label) || ['很差', '较差', '一般', '较好', '很好']"
            />
          </div>

          <!-- 排序题 -->
          <div v-else-if="field.type === 'ranking'" class="ranking-field">
            <draggable
              v-model="formData[field.id]"
              group="ranking"
              @start="drag = true"
              @end="drag = false"
              item-key="id"
            >
              <template #item="{ element, index }">
                <div class="ranking-item">
                  <span class="ranking-number">{{ index + 1 }}</span>
                  <span class="ranking-label">{{ element.label }}</span>
                  <el-icon class="drag-handle"><Rank /></el-icon>
                </div>
              </template>
            </draggable>
          </div>
        </el-form-item>
      </div>

      <!-- 提交按钮 -->
      <div class="form-actions">
        <el-button
          type="primary"
          size="large"
          @click="submitVote"
          :loading="submitting"
          :disabled="!canSubmit"
        >
          提交投票
        </el-button>
        <el-button size="large" @click="resetForm">
          重置
        </el-button>
      </div>
    </el-form>

    <!-- 结果展示 -->
    <div v-if="showResults" class="results-section">
      <h3>投票结果</h3>
      <VoteResults :results="voteResults" :schema="formSchema" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import draggable from 'vuedraggable'
import { Rank } from '@element-plus/icons-vue'
import VoteResults from './VoteResults.vue'
import { submitVote as submitVoteAPI, getVoteResults as getVoteResultsAPI } from '../api/index.js'

// Props
const props = defineProps({
  formSchema: {
    type: Object,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['submit', 'update:modelValue'])

// Refs
const formRef = ref()
const submitting = ref(false)
const showResults = ref(false)
const voteResults = ref(null)
const drag = ref(false)

// 响应式数据
const formData = reactive({})
const formRules = reactive({})

// 计算属性
const canSubmit = computed(() => {
  return Object.keys(formData).length > 0 && !submitting.value
})

// 初始化表单数据和验证规则
const initForm = () => {
  props.formSchema.fields.forEach(field => {
    // 初始化表单数据
    if (field.type === 'checkbox') {
      formData[field.id] = []
    } else if (field.type === 'ranking') {
      formData[field.id] = [...field.options]
    } else {
      formData[field.id] = ''
    }

    // 设置验证规则
    if (field.required) {
      formRules[field.id] = [
        {
          required: true,
          message: `请选择${field.label}`,
          trigger: 'change'
        }
      ]
    }

    // 添加自定义验证
    if (field.validation) {
      const customRules = []
      
      if (field.validation.min && field.type === 'checkbox') {
        customRules.push({
          type: 'array',
          min: field.validation.min,
          message: `至少选择${field.validation.min}项`,
          trigger: 'change'
        })
      }

      if (field.validation.max && field.type === 'checkbox') {
        customRules.push({
          type: 'array',
          max: field.validation.max,
          message: `最多选择${field.validation.max}项`,
          trigger: 'change'
        })
      }

      if (customRules.length > 0) {
        formRules[field.id] = formRules[field.id] 
          ? [...formRules[field.id], ...customRules]
          : customRules
      }
    }
  })
}

// 格式化时间
const formatTime = (timeStr) => {
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 提交投票
const submitVote = async () => {
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    const voteData = {
      formId: props.formSchema.id,
      answers: formData,
      timestamp: new Date().toISOString()
    }

    await submitVoteAPI(props.formSchema.id, formData)
    
    ElMessage.success('投票提交成功！')
    emit('submit', voteData)
    
    // 显示结果
    await loadResults()
    
  } catch (error) {
    console.error('投票提交失败:', error)
    ElMessage.error('投票提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
  initForm()
}

// 加载投票结果
const loadResults = async () => {
  try {
    const results = await getVoteResultsAPI(props.formSchema.id)
    voteResults.value = results
    showResults.value = true
  } catch (error) {
    console.error('加载结果失败:', error)
  }
}

// 监听表单数据变化
watch(formData, (newData) => {
  emit('update:modelValue', newData)
}, { deep: true })

// 组件挂载时初始化
onMounted(() => {
  initForm()
})
</script>

<style scoped>
.dynamic-vote-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.form-header {
  margin-bottom: 32px;
  text-align: center;
}

.form-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.form-description {
  font-size: 14px;
  color: #606266;
  margin-bottom: 16px;
  line-height: 1.6;
}

.form-meta {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.form-field {
  margin-bottom: 24px;
}

.field-description {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  line-height: 1.4;
}

.option-content {
  display: flex;
  flex-direction: column;
}

.option-label {
  font-weight: 500;
}

.option-description {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.radio-group,
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.radio-option,
.checkbox-option {
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  transition: all 0.3s;
}

.radio-option:hover,
.checkbox-option:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.rating-field {
  padding: 16px;
  text-align: center;
  background: #fafafa;
  border-radius: 6px;
}

.ranking-field {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  cursor: move;
  transition: all 0.3s;
}

.ranking-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.ranking-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #409eff;
  color: #fff;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  margin-right: 12px;
}

.ranking-label {
  flex: 1;
  font-weight: 500;
}

.drag-handle {
  color: #c0c4cc;
  cursor: grab;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

.results-section {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dynamic-vote-form {
    padding: 16px;
    margin: 16px;
  }
  
  .form-title {
    font-size: 20px;
  }
  
  .radio-group,
  .checkbox-group {
    gap: 8px;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
