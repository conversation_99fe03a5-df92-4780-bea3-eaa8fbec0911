<template>
  <div class="test-api">
    <h1>API测试页面</h1>
    
    <div class="test-section">
      <h2>基础连接测试</h2>
      <el-button @click="testBasicConnection" type="primary">测试基础连接</el-button>
      <div v-if="basicResult" :class="basicResult.success ? 'success' : 'error'">
        {{ basicResult.message }}
      </div>
    </div>

    <div class="test-section">
      <h2>AI API测试</h2>
      <el-input 
        v-model="testPrompt" 
        placeholder="输入测试提示词"
        style="width: 300px; margin-right: 10px;"
      />
      <el-button @click="testAiApi" type="success">测试AI生成</el-button>
      <div v-if="aiResult" :class="aiResult.success ? 'success' : 'error'">
        {{ aiResult.message }}
      </div>
    </div>

    <div class="test-section">
      <h2>投票API测试</h2>
      <el-button @click="testVoteApi" type="warning">测试投票接口</el-button>
      <div v-if="voteResult" :class="voteResult.success ? 'success' : 'error'">
        {{ voteResult.message }}
      </div>
    </div>

    <div class="test-section">
      <h2>API响应日志</h2>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span :class="log.type">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { testApi, generateVoteSchema, getVoteList } from './api/index.js'

// 响应式数据
const basicResult = ref(null)
const aiResult = ref(null)
const voteResult = ref(null)
const testPrompt = ref('创建一个关于午餐选择的投票')
const logs = ref([])

// 添加日志
const addLog = (message, type = 'info') => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
  if (logs.value.length > 20) {
    logs.value.pop()
  }
}

// 测试基础连接
const testBasicConnection = async () => {
  try {
    addLog('开始测试基础连接...', 'info')
    const response = await testApi()
    addLog('基础连接测试成功', 'success')
    basicResult.value = { success: true, message: '连接正常' }
  } catch (error) {
    addLog(`基础连接测试失败: ${error.message}`, 'error')
    basicResult.value = { success: false, message: error.message }
  }
}

// 测试AI API
const testAiApi = async () => {
  try {
    addLog('开始测试AI API...', 'info')
    const response = await generateVoteSchema(testPrompt.value)
    addLog('AI API测试成功', 'success')
    aiResult.value = { success: true, message: 'AI接口正常' }
  } catch (error) {
    addLog(`AI API测试失败: ${error.message}`, 'error')
    aiResult.value = { success: false, message: error.message }
  }
}

// 测试投票API
const testVoteApi = async () => {
  try {
    addLog('开始测试投票API...', 'info')
    const response = await getVoteList({ current: 1, size: 10 })
    addLog('投票API测试成功', 'success')
    voteResult.value = { success: true, message: '投票接口正常' }
  } catch (error) {
    addLog(`投票API测试失败: ${error.message}`, 'error')
    voteResult.value = { success: false, message: error.message }
  }
}
</script>

<style scoped>
.test-api {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.success {
  color: #67c23a;
  margin-top: 10px;
}

.error {
  color: #f56c6c;
  margin-top: 10px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #999;
  margin-right: 10px;
}

.log-item .info {
  color: #409eff;
}

.log-item .success {
  color: #67c23a;
}

.log-item .error {
  color: #f56c6c;
}
</style>
