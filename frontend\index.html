<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI辅助投票生成系统</title>
  <meta name="description" content="基于AI技术的智能投票表单生成系统，支持多种投票类型和实时结果统计">
  <meta name="keywords" content="AI,投票,表单生成,Vue,智能系统">
  <meta name="author" content="AI Vote System">
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <link rel="icon" type="image/png" href="/favicon.png">
  
  <!-- 预加载关键资源 -->
  <link rel="preload" href="/fonts/inter.woff2" as="font" type="font/woff2" crossorigin>
  
  <!-- PWA支持 -->
  <link rel="manifest" href="/manifest.json">
  <meta name="theme-color" content="#409EFF">
  
  <!-- iOS Safari支持 -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="AI投票系统">
  <link rel="apple-touch-icon" href="/apple-touch-icon.png">
  
  <!-- 社交媒体分享 -->
  <meta property="og:title" content="AI辅助投票生成系统">
  <meta property="og:description" content="基于AI技术的智能投票表单生成系统">
  <meta property="og:image" content="/og-image.png">
  <meta property="og:url" content="https://vote.example.com">
  <meta property="og:type" content="website">
  
  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="AI辅助投票生成系统">
  <meta name="twitter:description" content="基于AI技术的智能投票表单生成系统">
  <meta name="twitter:image" content="/twitter-image.png">
  
  <!-- 安全策略 -->
  <meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline' 'unsafe-eval';
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: https:;
    font-src 'self' data:;
    connect-src 'self' ws: wss:;
  ">
  
  <style>
    /* 加载动画 */
    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.5s ease;
    }
    
    .loading-screen.fade-out {
      opacity: 0;
      pointer-events: none;
    }
    
    .loading-logo {
      width: 80px;
      height: 80px;
      margin-bottom: 24px;
      animation: pulse 2s infinite;
    }
    
    .loading-text {
      color: white;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 16px;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.1); }
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* 隐藏加载屏幕当应用准备就绪 */
    .app-ready .loading-screen {
      display: none;
    }
  </style>
</head>
<body>
  <!-- 加载屏幕 -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-logo">
      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </div>
    <div class="loading-text">AI辅助投票生成系统</div>
    <div class="loading-spinner"></div>
  </div>
  
  <!-- Vue应用挂载点 -->
  <div id="app"></div>
  
  <!-- 不支持JavaScript的提示 -->
  <noscript>
    <div style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #f5f7fa;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      font-family: Arial, sans-serif;
      text-align: center;
      padding: 20px;
    ">
      <h1 style="color: #303133; margin-bottom: 16px;">需要启用JavaScript</h1>
      <p style="color: #606266; line-height: 1.6;">
        此应用需要JavaScript才能正常运行。<br>
        请在浏览器设置中启用JavaScript，然后刷新页面。
      </p>
    </div>
  </noscript>
  
  <script type="module" src="/main.js"></script>
  
  <script>
    // 隐藏加载屏幕
    window.addEventListener('load', () => {
      setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
          loadingScreen.classList.add('fade-out');
          setTimeout(() => {
            loadingScreen.style.display = 'none';
          }, 500);
        }
      }, 1000);
    });
    
    // 错误处理
    window.addEventListener('error', (event) => {
      console.error('应用错误:', event.error);
    });
    
    // 未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      console.error('未处理的Promise拒绝:', event.reason);
    });
    
    // 性能监控
    if ('performance' in window) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const perfData = performance.getEntriesByType('navigation')[0];
          console.log('页面加载性能:', {
            domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
            loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
            totalTime: perfData.loadEventEnd - perfData.fetchStart
          });
        }, 0);
      });
    }
  </script>
</body>
</html>
