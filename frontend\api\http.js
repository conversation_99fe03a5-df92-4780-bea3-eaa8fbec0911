// 简化的HTTP请求封装
import axios from 'axios'

// 创建axios实例
const http = axios.create({
  baseURL: '/api',  // 使用Vite代理
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
http.interceptors.request.use(
  config => {
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    console.log('🚀 请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  error => {
    console.error('❌ 请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  response => {
    console.log('✅ 响应:', response.config.url, response.status)
    return response.data
  },
  error => {
    console.error('❌ 响应错误:', error.response?.status, error.message)
    
    if (error.response) {
      const { status } = error.response
      switch (status) {
        case 401:
          console.error('未授权')
          break
        case 404:
          console.error('接口不存在')
          break
        case 500:
          console.error('服务器错误')
          break
      }
    }
    
    return Promise.reject(error)
  }
)

export default http
