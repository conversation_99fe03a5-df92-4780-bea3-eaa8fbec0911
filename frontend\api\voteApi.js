// 投票相关API接口 - 重构版本
import http from './http'

/**
 * 创建投票
 */
export const createVote = async (voteData) => {
  return await http.post('/votes/create', voteData)
}

/**
 * 提交投票
 */
export const submitVote = async (voteId, answers) => {
  return await http.post('/votes/submit', {
    voteId,
    answers
  })
}

/**
 * 获取投票详情
 */
export const getVoteDetail = async (voteId) => {
  return await http.get(`/votes/${voteId}`)
}

/**
 * 获取投票结果
 */
export const getVoteResults = async (voteId, detailed = false) => {
  return await http.get(`/votes/${voteId}/results`, {
    params: { detailed }
  })
}

/**
 * 获取实时统计
 */
export const getRealTimeStats = async (voteId) => {
  return await http.get(`/votes/${voteId}/stats`)
}

/**
 * 获取投票列表
 */
export const getVoteList = async (params = {}) => {
  return await http.get('/votes/list', { params })
}

/**
 * 获取用户投票
 */
export const getUserVotes = async (params = {}) => {
  return await http.get('/votes/my', { params })
}

/**
 * 更新投票
 */
export const updateVote = async (voteId, updateData) => {
  return await http.put(`/votes/${voteId}`, updateData)
}

/**
 * 删除投票
 */
export const deleteVote = async (voteId) => {
  return await http.delete(`/votes/${voteId}`)
}

/**
 * 导出投票数据
 */
export const exportVoteData = async (voteId, format = 'excel') => {
  return await http.get(`/votes/${voteId}/export`, {
    params: { format },
    responseType: 'blob'
  })
}

/**
 * 获取投票统计
 */
export const getVoteStatistics = async (voteId) => {
  return await http.get(`/votes/${voteId}/statistics`)
}

/**
 * 复制投票
 */
export const copyVote = async (voteId) => {
  return await http.post(`/votes/${voteId}/copy`)
}

/**
 * 设置投票状态
 */
export const setVoteStatus = async (voteId, status) => {
  return await http.patch(`/votes/${voteId}/status`, { status })
}
